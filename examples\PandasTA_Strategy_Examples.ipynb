{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pandas TA ([pandas_ta](https://github.com/twopirllc/pandas-ta)) Strategies for Custom Technical Analysis\n", "\n", "## Topics\n", "- What is a Pandas TA Strategy?\n", "    - Builtin Strategies: __AllStrategy__ and __CommonStrategy__\n", "    - Creating Strategies\n", "- Watchlist Class\n", "    - Strategy Management and Execution\n", "    - **NOTE:** The **watchlist** module is independent of Pandas TA. To easily use it, copy it from your local pandas_ta installation directory into your project directory.\n", "- Indicator Composition/Chaining for more Complex Strategies\n", "    - Comprehensive Example: _MACD and RSI Momo with BBANDS and SMAs 50 & 200 and Cumulative Log Returns_"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Pandas TA v0.2.89b0\n", "To install the Latest Version:\n", "$ pip install -U git+https://github.com/twopirllc/pandas-ta\n", "\n", "Populating the interactive namespace from numpy and matplotlib\n"]}], "source": ["%matplotlib inline\n", "import datetime as dt\n", "\n", "from tqdm import tqdm\n", "\n", "import pandas as pd\n", "import pandas_ta as ta\n", "from alphaVantageAPI.alphavantage import AlphaVantage  # pip install alphaVantage-api\n", "\n", "from watchlist import Watchlist # Is this failing? If so, copy it locally. See above.\n", "\n", "print(f\"\\nPandas TA v{ta.version}\\nTo install the Latest Version:\\n$ pip install -U git+https://github.com/twopirllc/pandas-ta\\n\")\n", "%pylab inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# What is a Pandas TA Strategy?\n", "A _Strategy_ is a simple way to name and group your favorite TA indicators. Technically, a _Strategy_ is a simple Data Class to contain list of indicators and their parameters. __Note__: _Strategy_ is experimental and subject to change. Pandas TA comes with two basic Strategies: __AllStrategy__ and __CommonStrategy__.\n", "\n", "## Strategy Requirements:\n", "- _name_: Some short memorable string.  _Note_: Case-insensitive \"All\" is reserved.\n", "- _ta_: A list of dicts containing keyword arguments to identify the indicator and the indicator's arguments\n", "\n", "## Optional Requirements:\n", "- _description_: A more detailed description of what the Strategy tries to capture. Default: None\n", "- _created_: At datetime string of when it was created. Default: Automatically generated.\n", "\n", "### Things to note:\n", "- A Strategy will __fail__ when consumed by Pandas TA if there is no {\"kind\": \"indicator name\"} attribute."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Builtin Examples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All\n", "Default Values"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name = All\n", "description = All the indicators with their default settings. Pandas TA default.\n", "created = Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)\n", "ta = None\n"]}], "source": ["AllStrategy = ta.AllStrategy\n", "print(\"name =\", AllStrategy.name)\n", "print(\"description =\", AllStrategy.description)\n", "print(\"created =\", AllStrategy.created)\n", "print(\"ta =\", AllStrategy.ta)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Common\n", "Default Values"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name = Common Price and Volume SMAs\n", "description = Common Price SMAs: 10, 20, 50, 200 and Volume SMA: 20.\n", "created = Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)\n", "ta = [{'kind': 'sma', 'length': 10}, {'kind': 'sma', 'length': 20}, {'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}, {'kind': 'sma', 'close': 'volume', 'length': 20, 'prefix': 'VOL'}]\n"]}], "source": ["CommonStrategy = ta.CommonStrategy\n", "print(\"name =\", CommonStrategy.name)\n", "print(\"description =\", CommonStrategy.description)\n", "print(\"created =\", CommonStrategy.created)\n", "print(\"ta =\", CommonStrategy.ta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Creating Strategies\n", "Strategies require a **name** and an array of dicts containing the \"kind\" of indicator (\"sma\") and other potential parameters for **ta**."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Strategy A"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='A', ta=[{'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["custom_a = ta.Strategy(name=\"A\", ta=[{\"kind\": \"sma\", \"length\": 50}, {\"kind\": \"sma\", \"length\": 200}])\n", "custom_a"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Strategy B"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='B', ta=[{'kind': 'ema', 'length': 8}, {'kind': 'ema', 'length': 21}, {'kind': 'log_return', 'cumulative': True}, {'kind': 'rsi'}, {'kind': 'supertrend'}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["custom_b = ta.Strategy(name=\"B\", ta=[{\"kind\": \"ema\", \"length\": 8}, {\"kind\": \"ema\", \"length\": 21}, {\"kind\": \"log_return\", \"cumulative\": True}, {\"kind\": \"rsi\"}, {\"kind\": \"supertrend\"}])\n", "custom_b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bad Strategy. (Misspelled Indicator)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Runtime Failure', ta=[{'kind': 'percet_return'}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Misspelled indicator, will fail later when ran with Pandas TA\n", "custom_run_failure = ta.Strategy(name=\"Runtime Failure\", ta=[{\"kind\": \"percet_return\"}])\n", "custom_run_failure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Strategy Management and Execution with _Watchlist_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize AlphaVantage Data Source"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AlphaVantage(\n", "  end_point:str = https://www.alphavantage.co/query,\n", "  api_key:str = YOUR API KEY,\n", "  export:bool = True,\n", "  export_path:str = .,\n", "  output_size:str = full,\n", "  output:str = csv,\n", "  datatype:str = json,\n", "  clean:bool = True,\n", "  proxy:dict = {}\n", ")"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["AV = AlphaVantage(\n", "    api_key=\"YOUR API KEY\", premium=False,\n", "    output_size='full', clean=True,\n", "    export_path=\".\", export=True\n", ")\n", "AV"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create Watchlist and set it's 'ds' to AlphaVantage"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["data_source = \"av\" # Default\n", "# data_source = \"yahoo\"\n", "watch = Watchlist([\"SPY\", \"IWM\"], ds_name=data_source, timed=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Info about the Watchlist. Note, the default Strategy is \"All\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Watch(name='Watch: SPY, IWM', ds_name='av', tickers[2]='SPY, IWM', tf='D', strategy[5]='Common Price and Volume SMAs')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["watch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Help about Watchlist"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on class Watchlist in module watchlist:\n", "\n", "class Watchlist(builtins.object)\n", " |  Watchlist(tickers: list, tf: str = None, name: str = None, strategy: pandas_ta.core.Strategy = None, ds_name: str = 'av', **kwargs)\n", " |  \n", " |  # Watchlist Class (** This is subject to change! **)\n", " |  A simple Class to load/download financial market data and automatically\n", " |  apply Technical Analysis indicators with a Pandas TA Strategy.\n", " |  \n", " |  Default Strategy: pandas_ta.CommonStrategy\n", " |  \n", " |  ## Package Support:\n", " |  ### Data Source (Default: AlphaVantage)\n", " |  - AlphaVantage (pip install alphaVantage-api).\n", " |  - Python Binance (pip install python-binance). # Future Support\n", " |  - Yahoo Finance (pip install yfinance). # Almost Supported\n", " |  \n", " |  # Technical Analysis:\n", " |  - Pandas TA (pip install pandas_ta)\n", " |  \n", " |  ## Required Arguments:\n", " |  - tickers: A list of strings containing tickers. Example: [\"SPY\", \"AAPL\"]\n", " |  \n", " |  Methods defined here:\n", " |  \n", " |  __init__(self, tickers: list, tf: str = None, name: str = None, strategy: pandas_ta.core.Strategy = None, ds_name: str = 'av', **kwargs)\n", " |      Initialize self.  See help(type(self)) for accurate signature.\n", " |  \n", " |  __repr__(self) -> str\n", " |      Return repr(self).\n", " |  \n", " |  indicators(self, *args, **kwargs) -> <built-in function any>\n", " |      Returns the list of indicators that are available with Pandas Ta.\n", " |  \n", " |  load(self, ticker: str = None, tf: str = None, index: str = 'date', drop: list = [], plot: bool = False, **kwargs) -> pandas.core.frame.DataFrame\n", " |      Loads or Downloads (if a local csv does not exist) the data from the\n", " |      Data Source. When successful, it returns a Data Frame for the requested\n", " |      ticker. If no tickers are given, it loads all the tickers.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors defined here:\n", " |  \n", " |  __dict__\n", " |      dictionary for instance variables (if defined)\n", " |  \n", " |  __weakref__\n", " |      list of weak references to the object (if defined)\n", " |  \n", " |  data\n", " |      When not None, it contains a dictionary of DataFrames keyed by ticker. data = {\"SPY\": pd.DataFrame, ...}\n", " |  \n", " |  name\n", " |      The name of the Watchlist. Default: \"Watchlist: {Watchlist.tickers}\".\n", " |  \n", " |  strategy\n", " |      Sets a valid Strategy. Default: pandas_ta.CommonStrategy\n", " |  \n", " |  tf\n", " |      Alias for timeframe. Default: 'D'\n", " |  \n", " |  tickers\n", " |      tickers\n", " |      \n", " |      If a string, it it converted to a list. Example: \"AAPL\" -> [\"AAPL\"]\n", " |          * Does not accept, comma seperated strings.\n", " |      If a list, checks if it is a list of strings.\n", " |  \n", " |  verbose\n", " |      Toggle the verbose property. Default: False\n", "\n"]}], "source": ["help(Watchlist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Default Strategy is \"Common\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[!] Loading All: SPY, IWM\n", "[+] Downloading[av]: SPY[D]\n", "[+] Strategy: Common Price and Volume SMAs\n", "[i] Indicator arguments: {'timed': False, 'append': True}\n", "[i] Multiprocessing 5 indicators with 7 chunks and 8/8 cpus.\n", "[i] Total indicators: 5\n", "[i] Columns added: 5\n", "[i] Last Run: Saturday June 19, 2021, NYSE: 7:59:42, Local: 11:59:42 PDT, Day 170/365 (47.00%)\n", "[+] Downloading[av]: IWM[D]\n", "[+] Strategy: Common Price and Volume SMAs\n", "[i] Indicator arguments: {'timed': False, 'append': True}\n", "[i] Multiprocessing 5 indicators with 7 chunks and 8/8 cpus.\n", "[i] Total indicators: 5\n", "[i] Columns added: 5\n", "[i] Last Run: Saturday June 19, 2021, NYSE: 8:00:15, Local: 12:00:15 PDT, Day 170/365 (47.00%)\n"]}], "source": ["# No arguments loads all the tickers and applies the Strategy to each ticker.\n", "# The result can be accessed with Watchlist's 'data' property which returns a \n", "# dictionary keyed by ticker and DataFrames as values \n", "watch.load(verbose=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["'SPY: (5443, 10), IWM: (5299, 10)'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["\", \".join([f\"{t}: {d.shape}\" for t,d in watch.data.items()])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_10</th>\n", "      <th>SMA_20</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "      <th>VOL_SMA_20</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.4300</td>\n", "      <td>425.3700</td>\n", "      <td>423.1000</td>\n", "      <td>425.2600</td>\n", "      <td>42358478.0</td>\n", "      <td>422.067</td>\n", "      <td>419.2510</td>\n", "      <td>416.2142</td>\n", "      <td>377.72930</td>\n", "      <td>57830914.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.4200</td>\n", "      <td>425.4600</td>\n", "      <td>423.5400</td>\n", "      <td>424.4800</td>\n", "      <td>51508508.0</td>\n", "      <td>422.548</td>\n", "      <td>419.6990</td>\n", "      <td>416.5766</td>\n", "      <td>378.11005</td>\n", "      <td>57149878.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.6300</td>\n", "      <td>424.8700</td>\n", "      <td>419.9200</td>\n", "      <td>422.1100</td>\n", "      <td>79250069.0</td>\n", "      <td>422.726</td>\n", "      <td>420.2075</td>\n", "      <td>416.8964</td>\n", "      <td>378.46770</td>\n", "      <td>58121870.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.6700</td>\n", "      <td>423.0200</td>\n", "      <td>419.3200</td>\n", "      <td>421.9700</td>\n", "      <td>90949659.0</td>\n", "      <td>423.046</td>\n", "      <td>420.7630</td>\n", "      <td>417.2040</td>\n", "      <td>378.83100</td>\n", "      <td>57346000.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.0900</td>\n", "      <td>417.8281</td>\n", "      <td>414.7000</td>\n", "      <td>414.9200</td>\n", "      <td>118676302.0</td>\n", "      <td>422.278</td>\n", "      <td>420.7450</td>\n", "      <td>417.3320</td>\n", "      <td>379.14260</td>\n", "      <td>59378705.05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5443 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close       volume   SMA_10  \\\n", "date                                                                       \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625    4006500.0      NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937    6516900.0      NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000    7222300.0      NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312    7907500.0      NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750    7431500.0      NaN   \n", "...              ...       ...       ...       ...          ...      ...   \n", "2021-06-14  424.4300  425.3700  423.1000  425.2600   42358478.0  422.067   \n", "2021-06-15  425.4200  425.4600  423.5400  424.4800   51508508.0  422.548   \n", "2021-06-16  424.6300  424.8700  419.9200  422.1100   79250069.0  422.726   \n", "2021-06-17  421.6700  423.0200  419.3200  421.9700   90949659.0  423.046   \n", "2021-06-18  417.0900  417.8281  414.7000  414.9200  118676302.0  422.278   \n", "\n", "              SMA_20    SMA_50    SMA_200   VOL_SMA_20  \n", "date                                                    \n", "1999-11-01       NaN       NaN        NaN          NaN  \n", "1999-11-02       NaN       NaN        NaN          NaN  \n", "1999-11-03       NaN       NaN        NaN          NaN  \n", "1999-11-04       NaN       NaN        NaN          NaN  \n", "1999-11-05       NaN       NaN        NaN          NaN  \n", "...              ...       ...        ...          ...  \n", "2021-06-14  419.2510  416.2142  377.72930  57830914.60  \n", "2021-06-15  419.6990  416.5766  378.11005  57149878.95  \n", "2021-06-16  420.2075  416.8964  378.46770  58121870.50  \n", "2021-06-17  420.7630  417.2040  378.83100  57346000.85  \n", "2021-06-18  420.7450  417.3320  379.14260  59378705.05  \n", "\n", "[5443 rows x 10 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.data[\"SPY\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_10</th>\n", "      <th>SMA_20</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "      <th>VOL_SMA_20</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.4300</td>\n", "      <td>425.3700</td>\n", "      <td>423.1000</td>\n", "      <td>425.2600</td>\n", "      <td>42358478.0</td>\n", "      <td>422.067</td>\n", "      <td>419.2510</td>\n", "      <td>416.2142</td>\n", "      <td>377.72930</td>\n", "      <td>57830914.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.4200</td>\n", "      <td>425.4600</td>\n", "      <td>423.5400</td>\n", "      <td>424.4800</td>\n", "      <td>51508508.0</td>\n", "      <td>422.548</td>\n", "      <td>419.6990</td>\n", "      <td>416.5766</td>\n", "      <td>378.11005</td>\n", "      <td>57149878.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.6300</td>\n", "      <td>424.8700</td>\n", "      <td>419.9200</td>\n", "      <td>422.1100</td>\n", "      <td>79250069.0</td>\n", "      <td>422.726</td>\n", "      <td>420.2075</td>\n", "      <td>416.8964</td>\n", "      <td>378.46770</td>\n", "      <td>58121870.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.6700</td>\n", "      <td>423.0200</td>\n", "      <td>419.3200</td>\n", "      <td>421.9700</td>\n", "      <td>90949659.0</td>\n", "      <td>423.046</td>\n", "      <td>420.7630</td>\n", "      <td>417.2040</td>\n", "      <td>378.83100</td>\n", "      <td>57346000.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.0900</td>\n", "      <td>417.8281</td>\n", "      <td>414.7000</td>\n", "      <td>414.9200</td>\n", "      <td>118676302.0</td>\n", "      <td>422.278</td>\n", "      <td>420.7450</td>\n", "      <td>417.3320</td>\n", "      <td>379.14260</td>\n", "      <td>59378705.05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5443 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close       volume   SMA_10  \\\n", "date                                                                       \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625    4006500.0      NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937    6516900.0      NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000    7222300.0      NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312    7907500.0      NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750    7431500.0      NaN   \n", "...              ...       ...       ...       ...          ...      ...   \n", "2021-06-14  424.4300  425.3700  423.1000  425.2600   42358478.0  422.067   \n", "2021-06-15  425.4200  425.4600  423.5400  424.4800   51508508.0  422.548   \n", "2021-06-16  424.6300  424.8700  419.9200  422.1100   79250069.0  422.726   \n", "2021-06-17  421.6700  423.0200  419.3200  421.9700   90949659.0  423.046   \n", "2021-06-18  417.0900  417.8281  414.7000  414.9200  118676302.0  422.278   \n", "\n", "              SMA_20    SMA_50    SMA_200   VOL_SMA_20  \n", "date                                                    \n", "1999-11-01       NaN       NaN        NaN          NaN  \n", "1999-11-02       NaN       NaN        NaN          NaN  \n", "1999-11-03       NaN       NaN        NaN          NaN  \n", "1999-11-04       NaN       NaN        NaN          NaN  \n", "1999-11-05       NaN       NaN        NaN          NaN  \n", "...              ...       ...        ...          ...  \n", "2021-06-14  419.2510  416.2142  377.72930  57830914.60  \n", "2021-06-15  419.6990  416.5766  378.11005  57149878.95  \n", "2021-06-16  420.2075  416.8964  378.46770  58121870.50  \n", "2021-06-17  420.7630  417.2040  378.83100  57346000.85  \n", "2021-06-18  420.7450  417.3320  379.14260  59378705.05  \n", "\n", "[5443 rows x 10 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1152x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["watch.load(\"SPY\", plot=True, mas=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Easy to swap Strategies and run them"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running Simple Strategy A"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='A', ta=[{'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load custom_a into Watchlist and verify\n", "watch.strategy = custom_a\n", "# watch.debug = True\n", "watch.strategy"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded IWM[D]: IWM_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2000-05-26</th>\n", "      <td>91.06</td>\n", "      <td>91.4400</td>\n", "      <td>90.630</td>\n", "      <td>91.44</td>\n", "      <td>37400.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-05-30</th>\n", "      <td>92.75</td>\n", "      <td>94.8100</td>\n", "      <td>92.750</td>\n", "      <td>94.81</td>\n", "      <td>28800.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-05-31</th>\n", "      <td>95.13</td>\n", "      <td>96.3800</td>\n", "      <td>95.130</td>\n", "      <td>95.75</td>\n", "      <td>18000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-06-01</th>\n", "      <td>97.11</td>\n", "      <td>97.3100</td>\n", "      <td>97.110</td>\n", "      <td>97.31</td>\n", "      <td>3500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-06-02</th>\n", "      <td>101.70</td>\n", "      <td>102.4000</td>\n", "      <td>101.700</td>\n", "      <td>102.40</td>\n", "      <td>14700.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>232.31</td>\n", "      <td>233.3500</td>\n", "      <td>230.140</td>\n", "      <td>231.02</td>\n", "      <td>19182832.0</td>\n", "      <td>223.8902</td>\n", "      <td>197.88475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>231.12</td>\n", "      <td>231.4700</td>\n", "      <td>228.475</td>\n", "      <td>230.36</td>\n", "      <td>17039888.0</td>\n", "      <td>223.9980</td>\n", "      <td>198.25755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>229.70</td>\n", "      <td>230.6800</td>\n", "      <td>227.640</td>\n", "      <td>229.87</td>\n", "      <td>23546462.0</td>\n", "      <td>224.1092</td>\n", "      <td>198.62130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>229.24</td>\n", "      <td>230.2000</td>\n", "      <td>224.520</td>\n", "      <td>227.29</td>\n", "      <td>48292458.0</td>\n", "      <td>224.2412</td>\n", "      <td>198.98060</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>223.70</td>\n", "      <td>225.7375</td>\n", "      <td>221.130</td>\n", "      <td>222.13</td>\n", "      <td>54023731.0</td>\n", "      <td>224.2326</td>\n", "      <td>199.30520</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5299 rows × 7 columns</p>\n", "</div>"], "text/plain": ["              open      high      low   close      volume    SMA_50    SMA_200\n", "date                                                                          \n", "2000-05-26   91.06   91.4400   90.630   91.44     37400.0       NaN        NaN\n", "2000-05-30   92.75   94.8100   92.750   94.81     28800.0       NaN        NaN\n", "2000-05-31   95.13   96.3800   95.130   95.75     18000.0       NaN        NaN\n", "2000-06-01   97.11   97.3100   97.110   97.31      3500.0       NaN        NaN\n", "2000-06-02  101.70  102.4000  101.700  102.40     14700.0       NaN        NaN\n", "...            ...       ...      ...     ...         ...       ...        ...\n", "2021-06-14  232.31  233.3500  230.140  231.02  19182832.0  223.8902  197.88475\n", "2021-06-15  231.12  231.4700  228.475  230.36  17039888.0  223.9980  198.25755\n", "2021-06-16  229.70  230.6800  227.640  229.87  23546462.0  224.1092  198.62130\n", "2021-06-17  229.24  230.2000  224.520  227.29  48292458.0  224.2412  198.98060\n", "2021-06-18  223.70  225.7375  221.130  222.13  54023731.0  224.2326  199.30520\n", "\n", "[5299 rows x 7 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.load(\"IWM\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running Simple Strategy B"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='B', ta=[{'kind': 'ema', 'length': 8}, {'kind': 'ema', 'length': 21}, {'kind': 'log_return', 'cumulative': True}, {'kind': 'rsi'}, {'kind': 'supertrend'}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load custom_b into Watchlist and verify\n", "watch.strategy = custom_b\n", "watch.strategy"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>EMA_8</th>\n", "      <th>EMA_21</th>\n", "      <th>CUMLOGRET_1</th>\n", "      <th>RSI_14</th>\n", "      <th>SUPERT_7_3.0</th>\n", "      <th>SUPERTd_7_3.0</th>\n", "      <th>SUPERTl_7_3.0</th>\n", "      <th>SUPERTs_7_3.0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.007172</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.000461</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.007120</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.016915</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.4300</td>\n", "      <td>425.3700</td>\n", "      <td>423.1000</td>\n", "      <td>425.2600</td>\n", "      <td>42358478.0</td>\n", "      <td>422.800725</td>\n", "      <td>419.952881</td>\n", "      <td>1.143268</td>\n", "      <td>64.359777</td>\n", "      <td>414.067589</td>\n", "      <td>1</td>\n", "      <td>414.067589</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.4200</td>\n", "      <td>425.4600</td>\n", "      <td>423.5400</td>\n", "      <td>424.4800</td>\n", "      <td>51508508.0</td>\n", "      <td>423.173897</td>\n", "      <td>420.364438</td>\n", "      <td>1.141432</td>\n", "      <td>62.201133</td>\n", "      <td>414.647404</td>\n", "      <td>1</td>\n", "      <td>414.647404</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.6300</td>\n", "      <td>424.8700</td>\n", "      <td>419.9200</td>\n", "      <td>422.1100</td>\n", "      <td>79250069.0</td>\n", "      <td>422.937476</td>\n", "      <td>420.523125</td>\n", "      <td>1.135833</td>\n", "      <td>56.049676</td>\n", "      <td>414.647404</td>\n", "      <td>1</td>\n", "      <td>414.647404</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.6700</td>\n", "      <td>423.0200</td>\n", "      <td>419.3200</td>\n", "      <td>421.9700</td>\n", "      <td>90949659.0</td>\n", "      <td>422.722481</td>\n", "      <td>420.654659</td>\n", "      <td>1.135501</td>\n", "      <td>55.699252</td>\n", "      <td>414.647404</td>\n", "      <td>1</td>\n", "      <td>414.647404</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.0900</td>\n", "      <td>417.8281</td>\n", "      <td>414.7000</td>\n", "      <td>414.9200</td>\n", "      <td>118676302.0</td>\n", "      <td>420.988596</td>\n", "      <td>420.133327</td>\n", "      <td>1.118653</td>\n", "      <td>41.596038</td>\n", "      <td>414.647404</td>\n", "      <td>1</td>\n", "      <td>414.647404</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5443 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close       volume       EMA_8  \\\n", "date                                                                          \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625    4006500.0         NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937    6516900.0         NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000    7222300.0         NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312    7907500.0         NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750    7431500.0         NaN   \n", "...              ...       ...       ...       ...          ...         ...   \n", "2021-06-14  424.4300  425.3700  423.1000  425.2600   42358478.0  422.800725   \n", "2021-06-15  425.4200  425.4600  423.5400  424.4800   51508508.0  423.173897   \n", "2021-06-16  424.6300  424.8700  419.9200  422.1100   79250069.0  422.937476   \n", "2021-06-17  421.6700  423.0200  419.3200  421.9700   90949659.0  422.722481   \n", "2021-06-18  417.0900  417.8281  414.7000  414.9200  118676302.0  420.988596   \n", "\n", "                EMA_21  CUMLOGRET_1     RSI_14  SUPERT_7_3.0  SUPERTd_7_3.0  \\\n", "date                                                                          \n", "1999-11-01         NaN     0.000000        NaN      0.000000              1   \n", "1999-11-02         NaN    -0.007172        NaN           NaN              1   \n", "1999-11-03         NaN    -0.000461        NaN           NaN              1   \n", "1999-11-04         NaN     0.007120        NaN           NaN              1   \n", "1999-11-05         NaN     0.016915        NaN           NaN              1   \n", "...                ...          ...        ...           ...            ...   \n", "2021-06-14  419.952881     1.143268  64.359777    414.067589              1   \n", "2021-06-15  420.364438     1.141432  62.201133    414.647404              1   \n", "2021-06-16  420.523125     1.135833  56.049676    414.647404              1   \n", "2021-06-17  420.654659     1.135501  55.699252    414.647404              1   \n", "2021-06-18  420.133327     1.118653  41.596038    414.647404              1   \n", "\n", "            SUPERTl_7_3.0  SUPERTs_7_3.0  \n", "date                                      \n", "1999-11-01            NaN            NaN  \n", "1999-11-02            NaN            NaN  \n", "1999-11-03            NaN            NaN  \n", "1999-11-04            NaN            NaN  \n", "1999-11-05            NaN            NaN  \n", "...                   ...            ...  \n", "2021-06-14     414.067589            NaN  \n", "2021-06-15     414.647404            NaN  \n", "2021-06-16     414.647404            NaN  \n", "2021-06-17     414.647404            NaN  \n", "2021-06-18     414.647404            NaN  \n", "\n", "[5443 rows x 13 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.load(\"SPY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running Bad Strategy. (Misspelled indicator)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Runtime Failure', ta=[{'kind': 'percet_return'}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load custom_run_failure into Watchlist and verify\n", "watch.strategy = custom_run_failure\n", "watch.strategy"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded IWM[D]: IWM_D.csv\n", "[X] Oops! 'AnalysisIndicators' object has no attribute 'percet_return'\n"]}], "source": ["try:\n", "    iwm = watch.load(\"IWM\")\n", "except Attribute<PERSON><PERSON><PERSON> as error:\n", "    print(f\"[X] Oops! {error}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Indicator Composition/Chaining\n", "- When you need an indicator to depend on the value of a prior indicator\n", "- Utilitze _prefix_ or _suffix_ to help identify unique columns or avoid column name clashes."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Volume MAs and MA chains"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Volume MAs and Price MA chain', ta=[{'kind': 'ema', 'close': 'volume', 'length': 10, 'prefix': 'VOLUME'}, {'kind': 'sma', 'close': 'volume', 'length': 20, 'prefix': 'VOLUME'}, {'kind': 'ema', 'length': 5}, {'kind': 'linreg', 'close': 'EMA_5', 'length': 8, 'prefix': 'EMA_5'}], description='TA Description', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set EMA's and SMA's 'close' to 'volume' to create Volume MAs, prefix 'volume' MAs with 'VOLUME' so easy to identify the column\n", "# Take a price EMA and apply LINREG from EMA's output\n", "volmas_price_ma_chain = [\n", "    {\"kind\":\"ema\", \"close\": \"volume\", \"length\": 10, \"prefix\": \"VOLUME\"},\n", "    {\"kind\":\"sma\", \"close\": \"volume\", \"length\": 20, \"prefix\": \"VOLUME\"},\n", "    {\"kind\":\"ema\", \"length\": 5},\n", "    {\"kind\":\"linreg\", \"close\": \"EMA_5\", \"length\": 8, \"prefix\": \"EMA_5\"},\n", "]\n", "vp_ma_chain_ta = ta.Strategy(\"Volume MAs and Price MA chain\", volmas_price_ma_chain)\n", "vp_ma_chain_ta"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Volume MAs and Price MA chain'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = vp_ma_chain_ta\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>VOLUME_EMA_10</th>\n", "      <th>VOLUME_SMA_20</th>\n", "      <th>EMA_5</th>\n", "      <th>EMA_5_LR_8</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>136.012480</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.4300</td>\n", "      <td>425.3700</td>\n", "      <td>423.1000</td>\n", "      <td>425.2600</td>\n", "      <td>42358478.0</td>\n", "      <td>5.054864e+07</td>\n", "      <td>57830914.60</td>\n", "      <td>423.686083</td>\n", "      <td>422.946481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.4200</td>\n", "      <td>425.4600</td>\n", "      <td>423.5400</td>\n", "      <td>424.4800</td>\n", "      <td>51508508.0</td>\n", "      <td>5.072316e+07</td>\n", "      <td>57149878.95</td>\n", "      <td>423.950722</td>\n", "      <td>423.426186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.6300</td>\n", "      <td>424.8700</td>\n", "      <td>419.9200</td>\n", "      <td>422.1100</td>\n", "      <td>79250069.0</td>\n", "      <td>5.590987e+07</td>\n", "      <td>58121870.50</td>\n", "      <td>423.337148</td>\n", "      <td>423.580830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.6700</td>\n", "      <td>423.0200</td>\n", "      <td>419.3200</td>\n", "      <td>421.9700</td>\n", "      <td>90949659.0</td>\n", "      <td>6.228074e+07</td>\n", "      <td>57346000.85</td>\n", "      <td>422.881432</td>\n", "      <td>423.493411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.0900</td>\n", "      <td>417.8281</td>\n", "      <td>414.7000</td>\n", "      <td>414.9200</td>\n", "      <td>118676302.0</td>\n", "      <td>7.253448e+07</td>\n", "      <td>59378705.05</td>\n", "      <td>420.227621</td>\n", "      <td>422.469933</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5443 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close       volume  \\\n", "date                                                              \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625    4006500.0   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937    6516900.0   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000    7222300.0   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312    7907500.0   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750    7431500.0   \n", "...              ...       ...       ...       ...          ...   \n", "2021-06-14  424.4300  425.3700  423.1000  425.2600   42358478.0   \n", "2021-06-15  425.4200  425.4600  423.5400  424.4800   51508508.0   \n", "2021-06-16  424.6300  424.8700  419.9200  422.1100   79250069.0   \n", "2021-06-17  421.6700  423.0200  419.3200  421.9700   90949659.0   \n", "2021-06-18  417.0900  417.8281  414.7000  414.9200  118676302.0   \n", "\n", "            VOLUME_EMA_10  VOLUME_SMA_20       EMA_5  EMA_5_LR_8  \n", "date                                                              \n", "1999-11-01            NaN            NaN         NaN         NaN  \n", "1999-11-02            NaN            NaN         NaN         NaN  \n", "1999-11-03            NaN            NaN         NaN         NaN  \n", "1999-11-04            NaN            NaN         NaN         NaN  \n", "1999-11-05            NaN            NaN  136.012480         NaN  \n", "...                   ...            ...         ...         ...  \n", "2021-06-14   5.054864e+07    57830914.60  423.686083  422.946481  \n", "2021-06-15   5.072316e+07    57149878.95  423.950722  423.426186  \n", "2021-06-16   5.590987e+07    58121870.50  423.337148  423.580830  \n", "2021-06-17   6.228074e+07    57346000.85  422.881432  423.493411  \n", "2021-06-18   7.253448e+07    59378705.05  420.227621  422.469933  \n", "\n", "[5443 rows x 9 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "spy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### MACD BBANDS"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='MACD BBands', ta=[{'kind': 'macd'}, {'kind': 'bbands', 'close': 'MACD_12_26_9', 'length': 20, 'ddof': 0, 'prefix': 'MACD'}], description='BBANDS_20 applied to MACD', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# MACD is the initial indicator that BBANDS depends on.\n", "# Set BBANDS's 'close' to MACD's main signal, in this case 'MACD_12_26_9' and add a prefix (or suffix) so it's easier to identify\n", "macd_bands_ta = [\n", "    {\"kind\":\"macd\"},\n", "    {\"kind\":\"bbands\", \"close\": \"MACD_12_26_9\", \"length\": 20, \"ddof\": 0, \"prefix\": \"MACD\"}\n", "]\n", "macd_bands_ta = ta.Strategy(\"MACD BBands\", macd_bands_ta, f\"BBANDS_{macd_bands_ta[1]['length']} applied to MACD\")\n", "macd_bands_ta"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["'MACD BBands'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = macd_bands_ta\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>MACD_12_26_9</th>\n", "      <th>MACDh_12_26_9</th>\n", "      <th>MACDs_12_26_9</th>\n", "      <th>MACD_BBL_20_2.0</th>\n", "      <th>MACD_BBM_20_2.0</th>\n", "      <th>MACD_BBU_20_2.0</th>\n", "      <th>MACD_BBB_20_2.0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.4300</td>\n", "      <td>425.3700</td>\n", "      <td>423.1000</td>\n", "      <td>425.2600</td>\n", "      <td>42358478.0</td>\n", "      <td>2.786483</td>\n", "      <td>0.324042</td>\n", "      <td>2.462441</td>\n", "      <td>1.018819</td>\n", "      <td>1.997419</td>\n", "      <td>2.976018</td>\n", "      <td>97.986402</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.4200</td>\n", "      <td>425.4600</td>\n", "      <td>423.5400</td>\n", "      <td>424.4800</td>\n", "      <td>51508508.0</td>\n", "      <td>2.795023</td>\n", "      <td>0.266066</td>\n", "      <td>2.528958</td>\n", "      <td>1.003087</td>\n", "      <td>2.040643</td>\n", "      <td>3.078199</td>\n", "      <td>101.689172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.6300</td>\n", "      <td>424.8700</td>\n", "      <td>419.9200</td>\n", "      <td>422.1100</td>\n", "      <td>79250069.0</td>\n", "      <td>2.580803</td>\n", "      <td>0.041476</td>\n", "      <td>2.539327</td>\n", "      <td>1.053702</td>\n", "      <td>2.091817</td>\n", "      <td>3.129932</td>\n", "      <td>99.254865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.6700</td>\n", "      <td>423.0200</td>\n", "      <td>419.3200</td>\n", "      <td>421.9700</td>\n", "      <td>90949659.0</td>\n", "      <td>2.372387</td>\n", "      <td>-0.133552</td>\n", "      <td>2.505939</td>\n", "      <td>1.200822</td>\n", "      <td>2.152386</td>\n", "      <td>3.103950</td>\n", "      <td>88.419455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.0900</td>\n", "      <td>417.8281</td>\n", "      <td>414.7000</td>\n", "      <td>414.9200</td>\n", "      <td>118676302.0</td>\n", "      <td>1.619669</td>\n", "      <td>-0.709015</td>\n", "      <td>2.328685</td>\n", "      <td>1.293478</td>\n", "      <td>2.173875</td>\n", "      <td>3.054273</td>\n", "      <td>80.997979</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5443 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close       volume  MACD_12_26_9  \\\n", "date                                                                            \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625    4006500.0           NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937    6516900.0           NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000    7222300.0           NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312    7907500.0           NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750    7431500.0           NaN   \n", "...              ...       ...       ...       ...          ...           ...   \n", "2021-06-14  424.4300  425.3700  423.1000  425.2600   42358478.0      2.786483   \n", "2021-06-15  425.4200  425.4600  423.5400  424.4800   51508508.0      2.795023   \n", "2021-06-16  424.6300  424.8700  419.9200  422.1100   79250069.0      2.580803   \n", "2021-06-17  421.6700  423.0200  419.3200  421.9700   90949659.0      2.372387   \n", "2021-06-18  417.0900  417.8281  414.7000  414.9200  118676302.0      1.619669   \n", "\n", "            MACDh_12_26_9  MACDs_12_26_9  MACD_BBL_20_2.0  MACD_BBM_20_2.0  \\\n", "date                                                                         \n", "1999-11-01            NaN            NaN              NaN              NaN   \n", "1999-11-02            NaN            NaN              NaN              NaN   \n", "1999-11-03            NaN            NaN              NaN              NaN   \n", "1999-11-04            NaN            NaN              NaN              NaN   \n", "1999-11-05            NaN            NaN              NaN              NaN   \n", "...                   ...            ...              ...              ...   \n", "2021-06-14       0.324042       2.462441         1.018819         1.997419   \n", "2021-06-15       0.266066       2.528958         1.003087         2.040643   \n", "2021-06-16       0.041476       2.539327         1.053702         2.091817   \n", "2021-06-17      -0.133552       2.505939         1.200822         2.152386   \n", "2021-06-18      -0.709015       2.328685         1.293478         2.173875   \n", "\n", "            MACD_BBU_20_2.0  MACD_BBB_20_2.0  \n", "date                                          \n", "1999-11-01              NaN              NaN  \n", "1999-11-02              NaN              NaN  \n", "1999-11-03              NaN              NaN  \n", "1999-11-04              NaN              NaN  \n", "1999-11-05              NaN              NaN  \n", "...                     ...              ...  \n", "2021-06-14         2.976018        97.986402  \n", "2021-06-15         3.078199       101.689172  \n", "2021-06-16         3.129932        99.254865  \n", "2021-06-17         3.103950        88.419455  \n", "2021-06-18         3.054273        80.997979  \n", "\n", "[5443 rows x 12 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "spy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Strategy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MACD and RSI Momentum with BBANDS and SMAs and Cumulative Log Returns"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Momo, Bands and SMAs and Cumulative Log Returns', ta=[{'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}, {'kind': 'bbands', 'length': 20, 'ddof': 0}, {'kind': 'macd'}, {'kind': 'rsi'}, {'kind': 'log_return', 'cumulative': True}, {'kind': 'sma', 'close': 'CUMLOGRET_1', 'length': 5, 'suffix': 'CUMLOGRET'}], description='MACD and RSI Momo with BBANDS and SMAs 50 & 200 and Cumulative Log Returns', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["momo_bands_sma_ta = [\n", "    {\"kind\":\"sma\", \"length\": 50},\n", "    {\"kind\":\"sma\", \"length\": 200},\n", "    {\"kind\":\"bbands\", \"length\": 20, \"ddof\": 0},\n", "    {\"kind\":\"macd\"},\n", "    {\"kind\":\"rsi\"},\n", "    {\"kind\":\"log_return\", \"cumulative\": True},\n", "    {\"kind\":\"sma\", \"close\": \"CUMLOGRET_1\", \"length\": 5, \"suffix\": \"CUMLOGRET\"},\n", "]\n", "momo_bands_sma_strategy = ta.Strategy(\n", "    \"Momo, Bands and SMAs and Cumulative Log Returns\", # name\n", "    momo_bands_sma_ta, # ta\n", "    \"MACD and RSI Momo with BBANDS and SMAs 50 & 200 and Cumulative Log Returns\" # description\n", ")\n", "momo_bands_sma_strategy"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Momo, Bands and SMAs and Cumulative Log Returns'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = momo_bands_sma_strategy\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "      <th>BBL_20_2.0</th>\n", "      <th>BBM_20_2.0</th>\n", "      <th>BBU_20_2.0</th>\n", "      <th>BBB_20_2.0</th>\n", "      <th>MACD_12_26_9</th>\n", "      <th>MACDh_12_26_9</th>\n", "      <th>MACDs_12_26_9</th>\n", "      <th>RSI_14</th>\n", "      <th>CUMLOGRET_1</th>\n", "      <th>SMA_5_CUMLOGRET</th>\n", "      <th>0</th>\n", "      <th>30</th>\n", "      <th>70</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.43</td>\n", "      <td>425.3700</td>\n", "      <td>423.10</td>\n", "      <td>425.26</td>\n", "      <td>42358478.0</td>\n", "      <td>416.2142</td>\n", "      <td>377.72930</td>\n", "      <td>411.623731</td>\n", "      <td>419.2510</td>\n", "      <td>426.878269</td>\n", "      <td>3.638522</td>\n", "      <td>2.786483</td>\n", "      <td>0.324042</td>\n", "      <td>2.462441</td>\n", "      <td>64.359777</td>\n", "      <td>1.143268</td>\n", "      <td>1.138932</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.42</td>\n", "      <td>425.4600</td>\n", "      <td>423.54</td>\n", "      <td>424.48</td>\n", "      <td>51508508.0</td>\n", "      <td>416.5766</td>\n", "      <td>378.11005</td>\n", "      <td>411.949365</td>\n", "      <td>419.6990</td>\n", "      <td>427.448635</td>\n", "      <td>3.692949</td>\n", "      <td>2.795023</td>\n", "      <td>0.266066</td>\n", "      <td>2.528958</td>\n", "      <td>62.201133</td>\n", "      <td>1.141432</td>\n", "      <td>1.139971</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.63</td>\n", "      <td>424.8700</td>\n", "      <td>419.92</td>\n", "      <td>422.11</td>\n", "      <td>79250069.0</td>\n", "      <td>416.8964</td>\n", "      <td>378.46770</td>\n", "      <td>413.268861</td>\n", "      <td>420.2075</td>\n", "      <td>427.146139</td>\n", "      <td>3.302482</td>\n", "      <td>2.580803</td>\n", "      <td>0.041476</td>\n", "      <td>2.539327</td>\n", "      <td>56.049676</td>\n", "      <td>1.135833</td>\n", "      <td>1.140189</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.67</td>\n", "      <td>423.0200</td>\n", "      <td>419.32</td>\n", "      <td>421.97</td>\n", "      <td>90949659.0</td>\n", "      <td>417.2040</td>\n", "      <td>378.83100</td>\n", "      <td>415.280617</td>\n", "      <td>420.7630</td>\n", "      <td>426.245383</td>\n", "      <td>2.605925</td>\n", "      <td>2.372387</td>\n", "      <td>-0.133552</td>\n", "      <td>2.505939</td>\n", "      <td>55.699252</td>\n", "      <td>1.135501</td>\n", "      <td>1.139413</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.09</td>\n", "      <td>417.8281</td>\n", "      <td>414.70</td>\n", "      <td>414.92</td>\n", "      <td>118676302.0</td>\n", "      <td>417.3320</td>\n", "      <td>379.14260</td>\n", "      <td>415.188859</td>\n", "      <td>420.7450</td>\n", "      <td>426.301141</td>\n", "      <td>2.641097</td>\n", "      <td>1.619669</td>\n", "      <td>-0.709015</td>\n", "      <td>2.328685</td>\n", "      <td>41.596038</td>\n", "      <td>1.118653</td>\n", "      <td>1.134938</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              open      high     low   close       volume    SMA_50  \\\n", "date                                                                  \n", "2021-06-14  424.43  425.3700  423.10  425.26   42358478.0  416.2142   \n", "2021-06-15  425.42  425.4600  423.54  424.48   51508508.0  416.5766   \n", "2021-06-16  424.63  424.8700  419.92  422.11   79250069.0  416.8964   \n", "2021-06-17  421.67  423.0200  419.32  421.97   90949659.0  417.2040   \n", "2021-06-18  417.09  417.8281  414.70  414.92  118676302.0  417.3320   \n", "\n", "              SMA_200  BBL_20_2.0  BBM_20_2.0  BBU_20_2.0  BBB_20_2.0  \\\n", "date                                                                    \n", "2021-06-14  377.72930  411.623731    419.2510  426.878269    3.638522   \n", "2021-06-15  378.11005  411.949365    419.6990  427.448635    3.692949   \n", "2021-06-16  378.46770  413.268861    420.2075  427.146139    3.302482   \n", "2021-06-17  378.83100  415.280617    420.7630  426.245383    2.605925   \n", "2021-06-18  379.14260  415.188859    420.7450  426.301141    2.641097   \n", "\n", "            MACD_12_26_9  MACDh_12_26_9  MACDs_12_26_9     RSI_14  \\\n", "date                                                                \n", "2021-06-14      2.786483       0.324042       2.462441  64.359777   \n", "2021-06-15      2.795023       0.266066       2.528958  62.201133   \n", "2021-06-16      2.580803       0.041476       2.539327  56.049676   \n", "2021-06-17      2.372387      -0.133552       2.505939  55.699252   \n", "2021-06-18      1.619669      -0.709015       2.328685  41.596038   \n", "\n", "            CUMLOGRET_1  SMA_5_CUMLOGRET  0  30  70  \n", "date                                                 \n", "2021-06-14     1.143268         1.138932  0  30  70  \n", "2021-06-15     1.141432         1.139971  0  30  70  \n", "2021-06-16     1.135833         1.140189  0  30  70  \n", "2021-06-17     1.135501         1.139413  0  30  70  \n", "2021-06-18     1.118653         1.134938  0  30  70  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "# Apply constants to the DataFrame for indicators\n", "spy.ta.constants(True, [0, 30, 70])\n", "spy.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Additional Strategy Options"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The ```params``` keyword takes a _tuple_ as a shorthand to the parameter arguments in order.\n", "* **Note**: If the indicator arguments change, so will results. Breaking Changes will **always** be posted on the README.\n", "\n", "The ```col_numbers``` keyword takes a _tuple_ specifying which column to return if the result is a DataFrame."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='EMA, MACD History, Outter BBands, Log Returns', ta=[{'kind': 'ema', 'params': (10,)}, {'kind': 'macd', 'params': (9, 19, 10), 'col_numbers': (1,)}, {'kind': 'bbands', 'col_numbers': (0, 2), 'col_names': ('LB', 'UB')}, {'kind': 'log_return', 'params': (5, False)}], description='EMA, MACD History, BBands(LB, UB), and Log Returns Strategy', created='Saturday June 19, 2021, NYSE: 7:59:38, Local: 11:59:38 PDT, Day 170/365 (47.00%)')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["params_ta = [\n", "    {\"kind\":\"ema\", \"params\": (10,)},\n", "    # params sets MACD's keyword arguments: fast=9, slow=19, signal=10\n", "    # and returning the 2nd column: histogram\n", "    {\"kind\":\"macd\", \"params\": (9, 19, 10), \"col_numbers\": (1,)},\n", "    # Selects the Lower and Upper Bands and renames them LB and UB, ignoring the MB\n", "    {\"kind\":\"bbands\", \"col_numbers\": (0,2), \"col_names\": (\"LB\", \"UB\")},\n", "    {\"kind\":\"log_return\", \"params\": (5, False)},\n", "]\n", "params_ta_strategy = ta.Strategy(\n", "    \"EMA, MACD History, Outter BBands, Log Returns\", # name\n", "    params_ta, # ta\n", "    \"EMA, MACD History, BBands(LB, UB), and Log Returns Strategy\" # description\n", ")\n", "params_ta_strategy"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["'EMA, MACD History, Outter BBands, Log Returns'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = params_ta_strategy\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>EMA_10</th>\n", "      <th>MACDh_9_19_10</th>\n", "      <th>LB</th>\n", "      <th>UB</th>\n", "      <th>LOGRET_5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.43</td>\n", "      <td>425.3700</td>\n", "      <td>423.10</td>\n", "      <td>425.26</td>\n", "      <td>42358478.0</td>\n", "      <td>422.270911</td>\n", "      <td>0.362543</td>\n", "      <td>420.791976</td>\n", "      <td>426.052024</td>\n", "      <td>0.007245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.42</td>\n", "      <td>425.4600</td>\n", "      <td>423.54</td>\n", "      <td>424.48</td>\n", "      <td>51508508.0</td>\n", "      <td>422.672563</td>\n", "      <td>0.270356</td>\n", "      <td>421.413575</td>\n", "      <td>426.310425</td>\n", "      <td>0.005196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.63</td>\n", "      <td>424.8700</td>\n", "      <td>419.92</td>\n", "      <td>422.11</td>\n", "      <td>79250069.0</td>\n", "      <td>422.570279</td>\n", "      <td>-0.022223</td>\n", "      <td>421.832167</td>\n", "      <td>426.075833</td>\n", "      <td>0.001090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.67</td>\n", "      <td>423.0200</td>\n", "      <td>419.32</td>\n", "      <td>421.97</td>\n", "      <td>90949659.0</td>\n", "      <td>422.461138</td>\n", "      <td>-0.230673</td>\n", "      <td>420.956510</td>\n", "      <td>426.295490</td>\n", "      <td>-0.003879</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.09</td>\n", "      <td>417.8281</td>\n", "      <td>414.70</td>\n", "      <td>414.92</td>\n", "      <td>118676302.0</td>\n", "      <td>421.090022</td>\n", "      <td>-0.927534</td>\n", "      <td>414.448692</td>\n", "      <td>429.047308</td>\n", "      <td>-0.022379</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              open      high     low   close       volume      EMA_10  \\\n", "date                                                                    \n", "2021-06-14  424.43  425.3700  423.10  425.26   42358478.0  422.270911   \n", "2021-06-15  425.42  425.4600  423.54  424.48   51508508.0  422.672563   \n", "2021-06-16  424.63  424.8700  419.92  422.11   79250069.0  422.570279   \n", "2021-06-17  421.67  423.0200  419.32  421.97   90949659.0  422.461138   \n", "2021-06-18  417.09  417.8281  414.70  414.92  118676302.0  421.090022   \n", "\n", "            MACDh_9_19_10          LB          UB  LOGRET_5  \n", "date                                                         \n", "2021-06-14       0.362543  420.791976  426.052024  0.007245  \n", "2021-06-15       0.270356  421.413575  426.310425  0.005196  \n", "2021-06-16      -0.022223  421.832167  426.075833  0.001090  \n", "2021-06-17      -0.230673  420.956510  426.295490 -0.003879  \n", "2021-06-18      -0.927534  414.448692  429.047308 -0.022379  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "spy.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Disclaimer\n", "* All investments involve risk, and the past performance of a security, industry, sector, market, financial product, trading strategy, or individual’s trading does not guarantee future results or returns. Investors are fully responsible for any investment decisions they make. Such decisions should be based solely on an evaluation of their financial circumstances, investment objectives, risk tolerance, and liquidity needs.\n", "\n", "* Any opinions, news, research, analyses, prices, or other information offered is provided as general market commentary, and does not constitute investment advice. I will not accept liability for any loss or damage, including without limitation any loss of profit, which may arise directly or indirectly from use of or reliance on such information."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 4}