{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Strategy Analysis with **Pandas TA** and AI/ML\n", "* This is a **Work in Progress** and subject to change!\n", "* Contributions are welcome and accepted!\n", "* Examples below are for **educational purposes only**.\n", "* **NOTE:** The **watchlist** module is independent of Pandas TA. To easily use it, copy it from your local pandas_ta installation directory into your project directory."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Required Packages\n", "##### Uncomment the packages you need to install or are missing"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#!pip install numpy\n", "#!pip install pandas\n", "#!pip install mplfinance\n", "#!pip install pandas-datareader\n", "#!pip install requests_cache\n", "#!pip install tqdm\n", "#!pip install alphaVantage-api # Required for Watchlist"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Populating the interactive namespace from numpy and matplotlib\n", "Numpy v1.20.2\n", "Pandas v1.2.4\n", "mplfinance v0.12.7a12\n", "\n", "Pandas TA v0.2.89b0\n", "To install the Latest Version:\n", "$ pip install -U git+https://github.com/twopirllc/pandas-ta\n", "\n"]}], "source": ["%pylab inline\n", "import datetime as dt\n", "import random as rnd\n", "from sys import float_info as sflt\n", "\n", "from tqdm import tqdm\n", "\n", "import numpy as np\n", "import pandas as pd\n", "pd.set_option(\"max_rows\", 100)\n", "pd.set_option(\"max_columns\", 20)\n", "\n", "import mplfinance as mpf\n", "import pandas_ta as ta\n", "\n", "from tqdm.notebook import trange, tqdm\n", "\n", "from watchlist import colors, Watchlist # Is this failing? If so, copy it locally. See above.\n", "\n", "print(f\"Numpy v{np.__version__}\")\n", "print(f\"Pandas v{pd.__version__}\")\n", "print(f\"mplfinance v{mpf.__version__}\")\n", "print(f\"\\nPandas TA v{ta.version}\\nTo install the Latest Version:\\n$ pip install -U git+https://github.com/twopirllc/pandas-ta\\n\")\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MISC Function(s)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def recent_bars(df, tf: str = \"1y\"):\n", "    # All Data: 0, Last Four Years: 0.25, Last Two Years: 0.5, This Year: 1, Last Half Year: 2, Last Quarter: 4\n", "    yearly_divisor = {\"all\": 0, \"10y\": 0.1, \"5y\": 0.2, \"4y\": 0.25, \"3y\": 1./3, \"2y\": 0.5, \"1y\": 1, \"6mo\": 2, \"3mo\": 4}\n", "    yd = yearly_divisor[tf] if tf in yearly_divisor.keys() else 0\n", "    return int(ta.RATE[\"TRADING_DAYS_PER_YEAR\"] / yd) if yd > 0 else df.shape[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Collection"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[!] Loading All: SPY, QQQ, AAPL, TSLA, BTC-USD\n", "[+] Downloading[yahoo]: SPY[D]\n", "[+] Saving: /Users/<USER>/av_data/SPY_D.csv\n", "[i] Runtime: 583.3476 ms (0.5833 s)\n", "[+] Downloading[yahoo]: QQQ[D]\n", "[+] Saving: /Users/<USER>/av_data/QQQ_D.csv\n", "[i] Runtime: 493.0341 ms (0.4930 s)\n", "[+] Downloading[yahoo]: AAPL[D]\n", "[+] Saving: /Users/<USER>/av_data/AAPL_D.csv\n", "[i] Runtime: 495.5222 ms (0.4955 s)\n", "[+] Downloading[yahoo]: TSLA[D]\n", "[+] Saving: /Users/<USER>/av_data/TSLA_D.csv\n", "[i] Runtime: 468.0090 ms (0.4680 s)\n", "[+] Downloading[yahoo]: BTC-USD[D]\n", "[+] Saving: /Users/<USER>/av_data/BTC-USD_D.csv\n", "[i] Runtime: 481.1203 ms (0.4811 s)\n"]}], "source": ["tf = \"D\"\n", "tickers = [\"SPY\", \"QQQ\", \"AAPL\", \"TSLA\", \"BTC-USD\"]\n", "watch = Watchlist(tickers, tf=tf, ds_name=\"yahoo\", timed=True)\n", "# watch.strategy = ta.CommonStrategy # If you have a Custom Strategy, you can use it here.\n", "watch.load(tickers, analyze=True, verbose=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Asset Selection"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPY (7149, 12)\n", "Columns: open, high, low, close, volume, dividends, split, SMA_10, SMA_20, SMA_50, SMA_200, VOL_SMA_20\n"]}], "source": ["ticker = tickers[0] # change tickers by changing the index\n", "print(f\"{ticker} {watch.data[ticker].shape}\\nColumns: {', '.join(list(watch.data[ticker].columns))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trim it"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>sma_10</th>\n", "      <th>sma_20</th>\n", "      <th>sma_50</th>\n", "      <th>sma_200</th>\n", "      <th>vol_sma_20</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-06-19</th>\n", "      <td>310.572014</td>\n", "      <td>310.779600</td>\n", "      <td>303.019496</td>\n", "      <td>305.105347</td>\n", "      <td>135549600</td>\n", "      <td>307.199380</td>\n", "      <td>304.275848</td>\n", "      <td>290.230250</td>\n", "      <td>294.865111</td>\n", "      <td>106968315.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-22</th>\n", "      <td>304.462768</td>\n", "      <td>307.487721</td>\n", "      <td>303.236978</td>\n", "      <td>307.062653</td>\n", "      <td>74649400</td>\n", "      <td>306.095767</td>\n", "      <td>305.090134</td>\n", "      <td>290.895322</td>\n", "      <td>294.957188</td>\n", "      <td>107502875.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-23</th>\n", "      <td>309.899768</td>\n", "      <td>310.898211</td>\n", "      <td>308.041294</td>\n", "      <td>308.476257</td>\n", "      <td>68471200</td>\n", "      <td>305.370712</td>\n", "      <td>305.795975</td>\n", "      <td>291.638665</td>\n", "      <td>295.055219</td>\n", "      <td>106478865.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-24</th>\n", "      <td>306.291601</td>\n", "      <td>306.953942</td>\n", "      <td>298.640252</td>\n", "      <td>300.607452</td>\n", "      <td>132813500</td>\n", "      <td>304.034952</td>\n", "      <td>305.889386</td>\n", "      <td>292.064598</td>\n", "      <td>295.113179</td>\n", "      <td>107878670.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-25</th>\n", "      <td>299.994539</td>\n", "      <td>304.116796</td>\n", "      <td>297.829618</td>\n", "      <td>303.830109</td>\n", "      <td>89468000</td>\n", "      <td>304.831430</td>\n", "      <td>306.171487</td>\n", "      <td>292.673680</td>\n", "      <td>295.187592</td>\n", "      <td>107831810.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>424.429993</td>\n", "      <td>425.369995</td>\n", "      <td>423.100006</td>\n", "      <td>425.260010</td>\n", "      <td>42358500</td>\n", "      <td>422.066998</td>\n", "      <td>419.250999</td>\n", "      <td>416.214198</td>\n", "      <td>376.217046</td>\n", "      <td>57830915.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>425.420013</td>\n", "      <td>425.459991</td>\n", "      <td>423.540009</td>\n", "      <td>424.480011</td>\n", "      <td>51508500</td>\n", "      <td>422.547998</td>\n", "      <td>419.699001</td>\n", "      <td>416.576599</td>\n", "      <td>376.617742</td>\n", "      <td>57149880.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>424.630005</td>\n", "      <td>424.869995</td>\n", "      <td>419.920013</td>\n", "      <td>422.109985</td>\n", "      <td>80386100</td>\n", "      <td>422.725998</td>\n", "      <td>420.207500</td>\n", "      <td>416.896398</td>\n", "      <td>376.995467</td>\n", "      <td>58178675.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>421.670013</td>\n", "      <td>423.019989</td>\n", "      <td>419.320007</td>\n", "      <td>421.970001</td>\n", "      <td>90949700</td>\n", "      <td>423.045999</td>\n", "      <td>420.763000</td>\n", "      <td>417.203998</td>\n", "      <td>377.378769</td>\n", "      <td>57402805.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>417.089996</td>\n", "      <td>417.829987</td>\n", "      <td>414.700012</td>\n", "      <td>414.920013</td>\n", "      <td>118573500</td>\n", "      <td>422.278000</td>\n", "      <td>420.745001</td>\n", "      <td>417.331999</td>\n", "      <td>377.710559</td>\n", "      <td>59430370.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>252 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                  open        high         low       close     volume  \\\n", "date                                                                    \n", "2020-06-19  310.572014  310.779600  303.019496  305.105347  135549600   \n", "2020-06-22  304.462768  307.487721  303.236978  307.062653   74649400   \n", "2020-06-23  309.899768  310.898211  308.041294  308.476257   68471200   \n", "2020-06-24  306.291601  306.953942  298.640252  300.607452  132813500   \n", "2020-06-25  299.994539  304.116796  297.829618  303.830109   89468000   \n", "...                ...         ...         ...         ...        ...   \n", "2021-06-14  424.429993  425.369995  423.100006  425.260010   42358500   \n", "2021-06-15  425.420013  425.459991  423.540009  424.480011   51508500   \n", "2021-06-16  424.630005  424.869995  419.920013  422.109985   80386100   \n", "2021-06-17  421.670013  423.019989  419.320007  421.970001   90949700   \n", "2021-06-18  417.089996  417.829987  414.700012  414.920013  118573500   \n", "\n", "                sma_10      sma_20      sma_50     sma_200   vol_sma_20  \n", "date                                                                     \n", "2020-06-19  307.199380  304.275848  290.230250  294.865111  106968315.0  \n", "2020-06-22  306.095767  305.090134  290.895322  294.957188  107502875.0  \n", "2020-06-23  305.370712  305.795975  291.638665  295.055219  106478865.0  \n", "2020-06-24  304.034952  305.889386  292.064598  295.113179  107878670.0  \n", "2020-06-25  304.831430  306.171487  292.673680  295.187592  107831810.0  \n", "...                ...         ...         ...         ...          ...  \n", "2021-06-14  422.066998  419.250999  416.214198  376.217046   57830915.0  \n", "2021-06-15  422.547998  419.699001  416.576599  376.617742   57149880.0  \n", "2021-06-16  422.725998  420.207500  416.896398  376.995467   58178675.0  \n", "2021-06-17  423.045999  420.763000  417.203998  377.378769   57402805.0  \n", "2021-06-18  422.278000  420.745001  417.331999  377.710559   59430370.0  \n", "\n", "[252 rows x 10 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["duration = \"1y\"\n", "asset = watch.data[ticker]\n", "recent = recent_bars(asset, duration)\n", "asset.columns = asset.columns.str.lower()\n", "asset.drop(columns=[\"dividends\", \"split\"], errors=\"ignore\", inplace=True)\n", "asset = asset.copy().tail(recent)\n", "asset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Trend Creation\n", "A **Trend** is the result of some calculation or condition of one or more indicators. For simplicity, a _Trend_ is either ```True``` or ```1``` and _No Trend_ is ```False``` or ```0```. Using the **Hello World** of Trends, the **Golden/Death Cross**, it's Trend is _Long_ when ```long = ma(close, 50) > ma(close, 200) ``` and _Short_ when ```short = ma(close, 50) < ma(close, 200) ```. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TA Columns Added:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sma_10</th>\n", "      <th>sma_20</th>\n", "      <th>sma_50</th>\n", "      <th>sma_200</th>\n", "      <th>vol_sma_20</th>\n", "      <th>EMA_8</th>\n", "      <th>EMA_21</th>\n", "      <th>EMA_50</th>\n", "      <th>PCTRET_1</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>422.066998</td>\n", "      <td>419.250999</td>\n", "      <td>416.214198</td>\n", "      <td>376.217046</td>\n", "      <td>57830915.0</td>\n", "      <td>422.800724</td>\n", "      <td>419.948691</td>\n", "      <td>413.793995</td>\n", "      <td>0.002239</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>422.547998</td>\n", "      <td>419.699001</td>\n", "      <td>416.576599</td>\n", "      <td>376.617742</td>\n", "      <td>57149880.0</td>\n", "      <td>423.173899</td>\n", "      <td>420.360629</td>\n", "      <td>414.213054</td>\n", "      <td>-0.001834</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>422.725998</td>\n", "      <td>420.207500</td>\n", "      <td>416.896398</td>\n", "      <td>376.995467</td>\n", "      <td>58178675.0</td>\n", "      <td>422.937474</td>\n", "      <td>420.519661</td>\n", "      <td>414.522738</td>\n", "      <td>-0.005583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>423.045999</td>\n", "      <td>420.763000</td>\n", "      <td>417.203998</td>\n", "      <td>377.378769</td>\n", "      <td>57402805.0</td>\n", "      <td>422.722480</td>\n", "      <td>420.651510</td>\n", "      <td>414.814787</td>\n", "      <td>-0.000332</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>422.278000</td>\n", "      <td>420.745001</td>\n", "      <td>417.331999</td>\n", "      <td>377.710559</td>\n", "      <td>59430370.0</td>\n", "      <td>420.988598</td>\n", "      <td>420.130465</td>\n", "      <td>414.818914</td>\n", "      <td>-0.016707</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                sma_10      sma_20      sma_50     sma_200  vol_sma_20  \\\n", "date                                                                     \n", "2021-06-14  422.066998  419.250999  416.214198  376.217046  57830915.0   \n", "2021-06-15  422.547998  419.699001  416.576599  376.617742  57149880.0   \n", "2021-06-16  422.725998  420.207500  416.896398  376.995467  58178675.0   \n", "2021-06-17  423.045999  420.763000  417.203998  377.378769  57402805.0   \n", "2021-06-18  422.278000  420.745001  417.331999  377.710559  59430370.0   \n", "\n", "                 EMA_8      EMA_21      EMA_50  PCTRET_1  \n", "date                                                      \n", "2021-06-14  422.800724  419.948691  413.793995  0.002239  \n", "2021-06-15  423.173899  420.360629  414.213054 -0.001834  \n", "2021-06-16  422.937474  420.519661  414.522738 -0.005583  \n", "2021-06-17  422.722480  420.651510  414.814787 -0.000332  \n", "2021-06-18  420.988598  420.130465  414.818914 -0.016707  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Example Long Trends\n", "# long = ta.sma(asset.close, 50) > ta.sma(asset.close, 200) # SMA(50) > SMA(200) \"Golden/Death Cross\"\n", "# long = ta.sma(asset.close, 10) > ta.sma(asset.close, 20) # SMA(10) > SMA(20)\n", "long = ta.ema(asset.close, 8) > ta.ema(asset.close, 21) # EMA(8) > EMA(21)\n", "# long = ta.increasing(ta.ema(asset.close, 50))\n", "# long = ta.macd(asset.close).iloc[:,1] > 0 # MACD Histogram is positive\n", "# long = ta.amat(asset.close, 50, 200).AMATe_LR_2  # Long Run of AMAT(50, 200) with lookback of 2 bars\n", "\n", "# long &= ta.increasing(ta.ema(asset.close, 50), 2) # Uncomment for further long restrictions, in this case when EMA(50) is increasing/sloping upwards\n", "# long = 1 - long # uncomment to create a short signal of the trend\n", "\n", "asset.ta.ema(length=8, sma=False, append=True)\n", "asset.ta.ema(length=21, sma=False, append=True)\n", "asset.ta.ema(length=50, sma=False, append=True)\n", "asset.ta.percent_return(append=True)\n", "print(\"TA Columns Added:\")\n", "asset[asset.columns[5:]].tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### **Trend Signals** \n", "Given a _Trend_, **Trend Signals** returns the _Trend_, _Trades_, _Entries_ and _Exits_ as boolean integers. When ```asbool=True```, it returns _Trends_, _Entries_ and _Exits_ as boolean values which is helpful when combined with the [**vectorbt**](https://github.com/polakowo/vectorbt) backtesting package."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TS_Trends</th>\n", "      <th>TS_Trades</th>\n", "      <th>TS_Entries</th>\n", "      <th>TS_Exits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-06-14</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-15</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-16</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-17</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-06-18</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            TS_Trends  TS_Trades  TS_Entries  TS_Exits\n", "date                                                  \n", "2021-06-14          1          0           0         0\n", "2021-06-15          1          0           0         0\n", "2021-06-16          1          0           0         0\n", "2021-06-17          1          0           0         0\n", "2021-06-18          1          0           0         0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["trendy = asset.ta.tsignals(long, asbool=False, append=True)\n", "trendy.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trend Entries & Exits & Trade Table\n", "This is a simple way to reduce the Asset DataFrame to a Trade Table with Dates, Signals, and Entries and Exits. Gives you an idea what to expect before running through a backtester such as [**vectorbt**](https://github.com/polakowo/vectorbt)."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current Trade:\n", "Price Entry | Last:\t415.2800 | 414.9200\n", "Unrealized PnL | %:\t-0.3600 | -0.0867%\n", "\n", "Trades Total | Round Trip:\t9 | 4\n", "Trade Coverage: 80.95%\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Signal</th>\n", "      <th>Entry</th>\n", "      <th>Exit</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-07-20</th>\n", "      <td>1</td>\n", "      <td>320.605774</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-09-11</th>\n", "      <td>-1</td>\n", "      <td>NaN</td>\n", "      <td>330.234192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-10-05</th>\n", "      <td>1</td>\n", "      <td>337.213409</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-10-28</th>\n", "      <td>-1</td>\n", "      <td>NaN</td>\n", "      <td>324.211609</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-11-05</th>\n", "      <td>1</td>\n", "      <td>347.614868</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-03</th>\n", "      <td>-1</td>\n", "      <td>NaN</td>\n", "      <td>380.174835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-10</th>\n", "      <td>1</td>\n", "      <td>388.308197</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-05-19</th>\n", "      <td>-1</td>\n", "      <td>NaN</td>\n", "      <td>410.859985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-05-20</th>\n", "      <td>1</td>\n", "      <td>415.279999</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Signal       Entry        Exit\n", "date                                      \n", "2020-07-20       1  320.605774         NaN\n", "2020-09-11      -1         NaN  330.234192\n", "2020-10-05       1  337.213409         NaN\n", "2020-10-28      -1         NaN  324.211609\n", "2020-11-05       1  347.614868         NaN\n", "2021-03-03      -1         NaN  380.174835\n", "2021-03-10       1  388.308197         NaN\n", "2021-05-19      -1         NaN  410.859985\n", "2021-05-20       1  415.279999         NaN"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["entries = trendy.TS_Entries * asset.close\n", "entries = entries[~np.isclose(entries, 0)]\n", "entries.dropna(inplace=True)\n", "entries.name = \"Entry\"\n", "\n", "exits = trendy.TS_Exits * asset.close\n", "exits = exits[~np.isclose(exits, 0)]\n", "exits.dropna(inplace=True)\n", "exits.name = \"Exit\"\n", "\n", "total_trades = trendy.TS_Trades.abs().sum()\n", "rt_trades = int(trendy.TS_Trades.abs().sum() // 2)\n", "\n", "all_trades = trendy.TS_Trades.copy().fillna(0)\n", "all_trades = all_trades[all_trades != 0]\n", "\n", "trades = pd.DataFrame({\n", "    \"Signal\": all_trades,\n", "    entries.name: entries,\n", "    exits.name: exits\n", "})\n", "\n", "# Show some stats if there is an active trade (when there is an odd number of round trip trades)\n", "if total_trades % 2 != 0:\n", "    unrealized_pnl = asset.close.iloc[-1] - entries.iloc[-1]\n", "    unrealized_pnl_pct_change = 100 * ((asset.close.iloc[-1] / entries.iloc[-1]) - 1)\n", "    print(\"Current Trade:\")\n", "    print(f\"Price Entry | Last:\\t{entries.iloc[-1]:.4f} | {asset.close.iloc[-1]:.4f}\")\n", "    print(f\"Unrealized PnL | %:\\t{unrealized_pnl:.4f} | {unrealized_pnl_pct_change:.4f}%\")\n", "print(f\"\\nTrades Total | Round Trip:\\t{total_trades} | {rt_trades}\")\n", "print(f\"Trade Coverage: {100 * asset.TS_Trends.sum() / asset.shape[0]:.2f}%\")\n", "\n", "trades"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Chart Display Strings"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["extime = ta.get_time(to_string=True)\n", "first_date, last_date = asset.index[0], asset.index[-1]\n", "f_date = f\"{first_date.day_name()} {first_date.month}-{first_date.day}-{first_date.year}\"\n", "l_date = f\"{last_date.day_name()} {last_date.month}-{last_date.day}-{last_date.year}\"\n", "last_ohlcv = f\"Last OHLCV: ({asset.iloc[-1].open:.4f}, {asset.iloc[-1].high:.4f}, {asset.iloc[-1].low:.4f}, {asset.iloc[-1].close:.4f}, {int(asset.iloc[-1].volume)})\"\n", "ptitle = f\"\\n{ticker} [{tf} for {duration}({recent} bars)] from {f_date} to {l_date}\\n{last_ohlcv}\\n{extime}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trade Chart"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:title={'center':'\\nSPY [D for 1y(252 bars)] from Friday 6-19-2020 to Friday 6-18-2021\\nLast OHLCV: (417.0900, 417.8300, 414.7000, 414.9200, 118573500)\\nSaturday June 19, 2021, NYSE: 8:06:33, Local: 12:06:33 PDT, Day 170/365 (47.00%)'}, xlabel='date'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************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\n", "text/plain": ["<Figure size 1152x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# chart = asset[\"close\"] #asset[[\"close\", \"SMA_10\", \"SMA_20\", \"SMA_50\", \"SMA_200\"]]\n", "# chart = asset[[\"close\", \"SMA_10\", \"SMA_20\"]]\n", "chart = asset[[\"close\", \"EMA_8\", \"EMA_21\", \"EMA_50\"]]\n", "chart.plot(figsize=(16, 10), color=colors(\"BkGrOrRd\"), title=ptitle, grid=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Long and Short Trends\n", "**Trends** are either a _Trend_ (```1```) or _No Trend_ (```0```) depending on the **Trend** passed into ***Trend Signals**"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='date'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1152x61.2 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["long_trend = trendy.TS_Trends\n", "short_trend = 1 - long_trend\n", "\n", "long_trend.plot(figsize=(16, 0.85), kind=\"area\", stacked=True, color=colors()[0], alpha=0.25) # Green Area\n", "short_trend.plot(figsize=(16, 0.85), kind=\"area\", stacked=True, color=colors()[1], alpha=0.25) # Red Area"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trades or Trade Signals\n", "The **Trades** are either _Enter_ (```1```) or _Exit_ (```-1```) or _No Position/Action_ (```0```). These are based on the **Trend** passed into **Trend Signals** whether they are _Long_ or _Short_ Trends."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='date'>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1152x108 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["trendy.TS_Trades.plot(figsize=(16, 1.5), color=colors(\"BkBl\")[0], grid=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Active Returns\n", "**Active Returns** are returns made during the course of the _Trend_. They are simply the product of the returns and the _Trend_"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.lines.Line2D at 0x12d219550>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************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\n", "text/plain": ["<Figure size 1152x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["asset[\"ACTRET_1\"] = trendy.TS_Trends.shift(1) * asset.PCTRET_1\n", "asset[[\"PCTRET_1\", \"ACTRET_1\"]].plot(figsize=(16, 3), color=colors(\"GyOr\"), alpha=1, grid=True).axhline(0, color=\"black\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Buy and Hold Returns (*PCTRET_1*) vs. Cum. Active Returns (*ACTRET_1*)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.lines.Line2D at 0x12d58aeb0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1152x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["((asset[[\"PCTRET_1\", \"ACTRET_1\"]] + 1).cumprod() - 1).plot(figsize=(16, 3), kind=\"area\", stacked=False, color=colors(\"GyOr\"), title=\"B&H vs. Cum. Active Returns\", alpha=.4, grid=True).axhline(0, color=\"black\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Disclaimer\n", "* All investments involve risk, and the past performance of a security, industry, sector, market, financial product, trading strategy, or individual’s trading does not guarantee future results or returns. Investors are fully responsible for any investment decisions they make. Such decisions should be based solely on an evaluation of their financial circumstances, investment objectives, risk tolerance, and liquidity needs.\n", "\n", "* Any opinions, news, research, analyses, prices, or other information offered is provided as general market commentary, and does not constitute investment advice. I will not accept liability for any loss or damage, including without limitation any loss of profit, which may arise directly or indirectly from use of or reliance on such information."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 4}