---
name: Feature request
about: Suggest an idea for this project.
title: ''
labels: enhancement
assignees: ''

---
**Which version are you running? The lastest version is on Github. Pip is for major releases.**
```python
import pandas_ta as ta
print(ta.version)
```

**Do you have _TA Lib_ also installed in your environment?**
```sh
$ pip list
```

**Upgrade.**
```sh
$ pip install -U git+https://github.com/twopirllc/pandas-ta
```

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.

Thanks for using Pandas TA!
